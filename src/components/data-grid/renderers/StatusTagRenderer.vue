<template>
  <Badge :class="badgeClasses">
    <Icon v-if="statusConfig.icon" :icon="statusConfig.icon" class="mr-1 inline-block" />
    {{ statusConfig.text }}
  </Badge>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Badge } from '@/components/ui/badge'
import { Icon } from '@iconify/vue'

interface StatusMap {
  [key: string]: {
    text: string
    variant?: 'default' | 'secondary' | 'destructive' | 'outline'
    icon?: string
  }
}

interface Props {
  /** 状态值 */
  status: string | number
  /** 状态映射配置 */
  statusMap?: StatusMap
  /** 默认变体 */
  defaultVariant?: 'default' | 'secondary' | 'destructive' | 'outline'
}

const props = withDefaults(defineProps<Props>(), {
  defaultVariant: 'default',
  statusMap: () => ({})
})

// 计算状态配置
const statusConfig = computed(() => {
  const statusKey = String(props.status)
  const config = props.statusMap[statusKey]
  
  if (config) {
    return config
  }
  
  // 默认配置
  return {
    text: statusKey,
    variant: props.defaultVariant,
  }
})

// 计算 Badge 样式类
const badgeClasses = computed(() => {
  const variant = statusConfig.value.variant || props.defaultVariant
  return `badge-${variant}`
})
</script>
