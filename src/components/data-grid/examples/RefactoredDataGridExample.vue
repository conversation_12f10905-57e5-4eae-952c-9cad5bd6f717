<template>
  <div class="p-6">
    <h1 class="text-2xl font-bold mb-4">重构后的 DataGrid 使用示例</h1>
    
    <DataGrid :columns="columns" :data="data">
      <!-- 状态列的自定义渲染 -->
      <template #cell-status="{ value }">
        <StatusTagRenderer 
          :status="value" 
          :status-map="statusMap" 
        />
      </template>
      
      <!-- 操作列的自定义渲染 -->
      <template #cell-actions="{ row }">
        <ActionButtonsRenderer 
          :row-data="row" 
          :actions="actionItems"
          :max-direct-actions="2"
        />
      </template>
      
      <!-- 用户名列的自定义渲染 -->
      <template #cell-name="{ value, row }">
        <div class="flex items-center gap-2">
          <div class="w-8 h-8 rounded-full bg-blue-500 text-white flex items-center justify-center text-sm">
            {{ value?.charAt(0)?.toUpperCase() }}
          </div>
          <div>
            <div class="font-medium">{{ value }}</div>
            <div class="text-sm text-gray-500">{{ row.email }}</div>
          </div>
        </div>
      </template>
    </DataGrid>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DataGrid from '@/components/data-grid/core/DataGrid.vue'
import { StatusTagRenderer, ActionButtonsRenderer } from '@/components/data-grid/renderers'
import type { ActionItem } from '@/components/data-grid/renderers'

// 列定义
const columns = ref([
  { field: 'name', header: '用户名', width: 200 },
  { field: 'email', header: '邮箱', width: 200 },
  { field: 'status', header: '状态', width: 120 },
  { field: 'actions', header: '操作', width: 150, fixed: 'right' }
])

// 示例数据
const data = ref([
  { 
    id: 1, 
    name: '张三', 
    email: '<EMAIL>', 
    status: 'active' 
  },
  { 
    id: 2, 
    name: '李四', 
    email: '<EMAIL>', 
    status: 'inactive' 
  },
  { 
    id: 3, 
    name: '王五', 
    email: '<EMAIL>', 
    status: 'pending' 
  }
])

// 状态映射配置
const statusMap = {
  active: { 
    text: '活跃', 
    variant: 'default' as const,
    icon: 'mdi:check-circle'
  },
  inactive: { 
    text: '非活跃', 
    variant: 'secondary' as const,
    icon: 'mdi:pause-circle'
  },
  pending: { 
    text: '待审核', 
    variant: 'outline' as const,
    icon: 'mdi:clock-outline'
  }
}

// 操作配置
const actionItems: ActionItem[] = [
  {
    key: 'edit',
    text: '编辑',
    icon: 'mdi:pencil',
    variant: 'outline',
    tooltip: '编辑用户',
    onClick: (rowData) => {
      console.log('编辑用户:', rowData)
      // 这里可以打开编辑对话框或跳转到编辑页面
    }
  },
  {
    key: 'delete',
    icon: 'mdi:delete',
    variant: 'destructive',
    tooltip: '删除用户',
    onClick: (rowData) => {
      console.log('删除用户:', rowData)
      // 这里可以显示确认对话框
    },
    visible: (rowData) => rowData.status !== 'active' // 只有非活跃用户才显示删除按钮
  },
  {
    key: 'view',
    text: '查看详情',
    icon: 'mdi:eye',
    variant: 'ghost',
    onClick: (rowData) => {
      console.log('查看用户详情:', rowData)
    }
  }
]
</script>
