# DataGrid 重构指南

## 重构概述

本次重构将 DataGrid 组件从基于插件的动态渲染器机制转换为基于 Vue 作用域插槽的静态组件架构。

## 重构目标

- **简化性**: 移除不必要的抽象层，简化组件架构
- **显式优于隐式**: 直接在模板中使用组件，而非通过字符串键动态查找
- **类型安全**: 充分利用 TypeScript 的优势，在编译时捕获错误
- **性能**: 利用静态导入和摇树优化，减少最终生产包的体积

## 架构变化

### 重构前（插件机制）

```javascript
// 旧的用法
const columns = [
  { field: 'status', header: '状态', renderer: 'status-tag-renderer' },
  { field: 'actions', header: '操作', renderer: 'action-buttons-renderer' },
]
```

### 重构后（作用域插槽）

```vue
<!-- 新的用法 -->
<DataGrid :columns="columns" :data="data">
  <template #cell-status="{ value, row, column }">
    <StatusTagRenderer :status="value" />
  </template>
  <template #cell-actions="{ row, column }">
    <ActionButtonsRenderer :rowData="row" />
  </template>
</DataGrid>
```

## 核心变化

### 1. DataGrid 组件变化

- **移除**: 插件管理器、动态组件加载、渲染器注册表
- **新增**: 作用域插槽支持，插槽名称遵循 `cell-<field>` 模式
- **简化**: 插槽渲染逻辑，直接传递上下文数据

### 2. 渲染器组件变化

- **位置**: 从 `@/src/components/data-grid/plugins/renderers` 移动到 `@/src/components/data-grid/renderers`
- **结构**: 转换为独立的、可直接导入的 Vue 组件
- **依赖**: 移除对插件系统的依赖，通过 props 接收数据

### 3. 类型系统变化

- **新增**: `CellSlotProps` 接口，定义作用域插槽的 props 类型
- **简化**: 移除插件相关的复杂类型定义

## 作用域插槽 API

### 插槽命名规则

- 单元格插槽: `cell-<field>`
- 例如: `cell-status`, `cell-actions`, `cell-name`

### 插槽 Props

```typescript
interface CellSlotProps {
  /** 当前单元格的值 */
  value: any
  /** 当前行的数据对象 */
  row: any
  /** 当前列的定义对象 */
  column: any
  /** 当前行的索引 */
  rowIndex?: number
  /** 当前列的索引 */
  columnIndex?: number
}
```

## 渲染器组件

### StatusTagRenderer

用于渲染状态标签的组件。

```vue
<template #cell-status="{ value }">
  <StatusTagRenderer :status="value" :status-map="statusMap" />
</template>
```

### ActionButtonsRenderer

用于渲染操作按钮的组件。

```vue
<template #cell-actions="{ row }">
  <ActionButtonsRenderer
    :row-data="row"
    :actions="actionItems"
    :max-direct-actions="2"
  />
</template>
```

## 迁移指南

### 步骤 1: 更新导入

```typescript
// 旧的导入
import { useDataGrid } from '@/components/data-grid'

// 新的导入
import DataGrid from '@/components/data-grid/core/DataGrid.vue'
import {
  StatusTagRenderer,
  ActionButtonsRenderer,
} from '@/components/data-grid/renderers'
```

### 步骤 2: 更新列定义

```typescript
// 旧的列定义
const columns = [
  { field: 'status', header: '状态', renderer: 'status-tag-renderer' },
  { field: 'actions', header: '操作', renderer: 'action-buttons-renderer' },
]

// 新的列定义
const columns = [
  { field: 'status', header: '状态' },
  { field: 'actions', header: '操作' },
]
```

### 步骤 3: 更新模板

```vue
<!-- 旧的模板 -->
<DataGrid :dataGridInstance="gridInstance" />

<!-- 新的模板 -->
<DataGrid :columns="columns" :data="data">
  <template #cell-status="{ value }">
    <StatusTagRenderer :status="value" :status-map="statusMap" />
  </template>
  <template #cell-actions="{ row }">
    <ActionButtonsRenderer :row-data="row" :actions="actionItems" />
  </template>
</DataGrid>
```

## 优势

1. **类型安全**: 编译时类型检查，减少运行时错误
2. **开发体验**: IDE 自动补全和类型提示
3. **性能优化**: 静态导入，支持摇树优化
4. **代码可读性**: 显式的组件使用，更容易理解和维护
5. **调试友好**: 直接的组件引用，更容易调试

## 完整对比示例

### 重构前的代码

```vue
<!-- 旧的实现方式 -->
<script setup>
import { useDataGrid } from '@/components/data-grid'

const userGrid = useDataGrid('user', {
  columns: [
    {
      field: 'status',
      header: '状态',
      renderer: 'status-tag-renderer',
      config: { statusMap: { active: { text: '活跃', type: 'success' } } },
    },
    {
      field: 'actions',
      header: '操作',
      renderer: 'action-buttons-renderer',
      config: { actions: [{ text: '编辑', onClick: editUser }] },
    },
  ],
})
</script>

<template>
  <DataGrid :dataGridInstance="userGrid" />
</template>
```

### 重构后的代码

```vue
<!-- 新的实现方式 -->
<script setup>
import DataGrid from '@/components/data-grid/core/DataGrid.vue'
import {
  StatusTagRenderer,
  ActionButtonsRenderer,
} from '@/components/data-grid/renderers'

const columns = [
  { field: 'status', header: '状态' },
  { field: 'actions', header: '操作' },
]

const statusMap = {
  active: { text: '活跃', variant: 'default' },
}

const actionItems = [{ text: '编辑', onClick: editUser }]
</script>

<template>
  <DataGrid :columns="columns" :data="data">
    <template #cell-status="{ value }">
      <StatusTagRenderer :status="value" :status-map="statusMap" />
    </template>
    <template #cell-actions="{ row }">
      <ActionButtonsRenderer :row-data="row" :actions="actionItems" />
    </template>
  </DataGrid>
</template>
```

## 注意事项

1. 确保所有使用到的渲染器组件都已正确导入
2. 插槽名称必须与列的 `field` 属性匹配
3. 渲染器组件的 props 需要根据实际需求进行调整
4. 旧的插件配置需要转换为新的组件 props
