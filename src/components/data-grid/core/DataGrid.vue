<template>
  <div
    class="flex flex-col overflow-hidden relative"
    :class="containerClasses"
    :style="containerStyle"
    ref="containerRef"
  >
    <!-- 使用 v-memo 优化工具栏渲染 -->
    <DGToolbar
      v-if="toolbarOptions"
      v-bind="toolbarOptions"
      ref="toolbarRef"
      class="flex-none"
      v-memo="[
        toolbarOptions?.title,
        toolbarOptions?.total,
        toolbarOptions?.queryParams?.offset,
        toolbarOptions?.queryParams?.limit,
      ]"
    />
    <div class="flex-1 min-h-0 relative" ref="gridContainerRef">
      <!-- 使用 v-memo 优化表格渲染，只有在关键属性变化时才重新渲染 -->
      <vxe-grid
        v-bind="processedGridOptionsWithoutToolbar"
        height="100%"
        ref="gridRef"
        v-on="gridEvents"
        v-memo="[
          processedGridOptionsWithoutToolbar.columns,
          processedGridOptionsWithoutToolbar.data,
          processedGridOptionsWithoutToolbar.loading,
        ]"
      >
        <!-- 动态渲染作用域插槽 -->
        <template
          v-for="(columnInfo, slotName) in cellSlots"
          :key="slotName"
          #[slotName]="params"
        >
          <slot
            :name="slotName"
            :value="params.row?.[columnInfo.field] || params.value"
            :row="params.row"
            :column="columnInfo"
            :rowIndex="params.rowIndex"
            :columnIndex="params.columnIndex"
          >
            <!-- 默认渲染：直接显示值 -->
            {{ params.row?.[columnInfo.field] || params.value }}
          </slot>
        </template>
      </vxe-grid>

      <!-- 自定义 Loading 遮罩层 -->
      <DataGridLoading :visible="isLoading" :config="loadingConfig" />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  ref,
  onMounted,
  onUnmounted,
  watch,
  nextTick,
  provide,
} from 'vue'
import { useResizeObserver } from '@vueuse/core'
import type { DataGridProps, CellSlotProps } from '../types'
import type { UseDataGridReturn } from '../types'
import DGToolbar from './DGToolbar.vue'
import DataGridLoading from './DataGridLoading.vue'
import { performanceMonitor } from '../utils/performanceMonitor'

/**
 * DataGrid 组件属性定义
 *
 * @interface DataGridPropsWithInstance
 * @extends DataGridProps
 */
interface DataGridPropsWithInstance extends DataGridProps {
  /**
   * 数据网格实例，通过 useDataGrid 创建
   * 如果提供此实例，将优先使用实例中的配置
   */
  dataGridInstance?: UseDataGridReturn
}

/**
 * DataGrid 组件的属性配置
 * 支持两种使用方式：
 * 1. 传入 dataGridInstance（推荐）
 * 2. 分别传入 moduleModel 和 gridOptions
 */
const props = withDefaults(defineProps<DataGridPropsWithInstance>(), {
  moduleModel: undefined,
  gridOptions: () => ({}),
  dataGridInstance: undefined,
})

// 提供 dataGridInstance 给子组件使用
provide('dataGridInstance', props.dataGridInstance)

// 性能监控
const componentId = computed(() => {
  return props.dataGridInstance?.moduleModel || props.moduleModel || 'unknown'
})

// 开始性能监控
if (import.meta.env.DEV) {
  onMounted(() => {
    performanceMonitor.startComponentMonitoring(componentId.value)
  })
}

const gridRef = ref()
const toolbarRef = ref()
const containerRef = ref()

// 性能优化：添加插槽计算缓存
const cellSlotsCache = new Map<string, Record<string, any>>()
const gridContainerRef = ref()

// 响应式的高度状态 - 使用 shallowRef 提高性能
const viewportHeight = shallowRef(window.innerHeight)
const containerOffsetTop = shallowRef(0)

// 智能获取有效的 gridOptions
// 优先级：显式传入的 gridOptions > dataGridInstance.gridOptions > 默认值
const effectiveGridOptions = computed(() => {
  // 如果显式传入了 gridOptions 且不为空对象，优先使用
  if (props.gridOptions && Object.keys(props.gridOptions).length > 0) {
    return props.gridOptions
  }

  // 如果传入了 dataGridInstance，使用其 gridOptions
  if (props.dataGridInstance?.gridOptions?.value) {
    return props.dataGridInstance.gridOptions.value
  }

  // 否则返回空对象作为默认值
  return {}
})

// 提取 toolbar 配置 - 使用浅层响应式避免深度监听
const toolbarOptions = computed(() => effectiveGridOptions.value.toolbarOptions)

// 提取 loading 状态和配置
const isLoading = computed(() => effectiveGridOptions.value.loading || false)
const loadingConfig = computed(() => ({
  ...effectiveGridOptions.value.loadingConfig,
  // 如果没有配置，使用默认配置
  type: effectiveGridOptions.value.loadingConfig?.type || 'spinner',
  text: effectiveGridOptions.value.loadingConfig?.text || '数据加载中...',
  size: effectiveGridOptions.value.loadingConfig?.size || 'medium',
  color: effectiveGridOptions.value.loadingConfig?.color || 'primary',
  theme: effectiveGridOptions.value.loadingConfig?.theme || 'auto',
}))

// 计算自动高度
const calculateAutoHeight = () => {
  if (containerRef.value) {
    const rect = containerRef.value.getBoundingClientRect()
    containerOffsetTop.value = rect.top
    const availableHeight = viewportHeight.value - rect.top - 20 // 减去20px底部边距
    return Math.max(availableHeight, 300) // 最小高度300px
  }
  return 600 // 默认高度
}

// 计算容器样式 - 处理用户设置的高度
const containerStyle = computed(() => {
  const styles: Record<string, string> = {}

  // 使用 effectiveGridOptions 获取正确的 height 配置
  const height = effectiveGridOptions.value.height

  // 如果用户设置了具体高度，应用到容器上
  if (height && height !== 'auto') {
    // 处理 number 类型的高度（转换为 px）
    if (typeof height === 'number' || !isNaN(Number(height))) {
      styles.height = height + 'px'
    } else {
      styles.height = height
    }
  } else {
    // height: 'auto' 或未设置时，计算可视区域高度
    const autoHeight = calculateAutoHeight()
    styles.height = autoHeight + 'px'
  }

  return styles
})

// 计算容器类名
const containerClasses = computed(() => {
  // 不管什么情况都不使用 h-full，而是用计算出的具体高度
  return ''
})

// 监听视窗尺寸变化
const updateViewportHeight = () => {
  viewportHeight.value = window.innerHeight
}

// 监听容器位置变化
useResizeObserver(containerRef, () => {
  nextTick(() => {
    if (containerRef.value) {
      const rect = containerRef.value.getBoundingClientRect()
      containerOffsetTop.value = rect.top
    }
  })
})

// 移除未使用的计算属性和注释掉的代码

// 获取事件处理器
const gridEvents = computed(() => {
  return effectiveGridOptions.value.gridEvents || {}
})

// 处理后的网格选项（不包含工具栏）
const processedGridOptionsWithoutToolbar = computed(() => {
  const processed = processedGridOptions.value
  // 移除工具栏配置，因为我们使用自定义工具栏
  const {
    toolbarOptions,
    height,
    loading,
    loadingConfig,
    ...gridOptionsWithoutToolbar
  } = processed
  return {
    ...gridOptionsWithoutToolbar,
    // 确保 vxe-grid 正确处理滚动
    autoResize: false, // 禁用自动调整，使用固定的 100% 高度
    // 添加这些配置确保只有内部滚动
    syncResize: true,
    keepSource: false,
    // 禁用 vxe-grid 的内置 loading，使用我们的自定义 loading
    loading: false,
  }
})

// 简化的插槽名称生成
const slotNameMap = new Map<string, string>()

// 生成稳定的插槽名称
const generateSlotName = (col: any, slotType: string): string => {
  const key = `${col.field || col.type || 'col'}_${slotType}`

  if (!slotNameMap.has(key)) {
    slotNameMap.set(key, `${key}_${Math.random().toString(36).substring(2, 9)}`)
  }

  return slotNameMap.get(key)!
}

// 清理资源
onBeforeUnmount(() => {
  slotNameMap.clear()
  // 清理插槽计算缓存
  customSlotsCache.clear()
  // 清理组件缓存
  componentCache.clear()
  // 清理预缓存的组件类型
  cachedComponentTypes.value.clear()
})

// 监听列配置变化时清理缓存
watch(
  () => effectiveGridOptions.value.columns,
  () => slotNameMap.clear(),
  { deep: true }
)

// 递归处理列配置的通用函数
const processColumnsRecursively = (
  cols: any[],
  processor: (col: any) => void
) => {
  cols.forEach((col) => {
    processor(col)
    if (col.children && Array.isArray(col.children)) {
      processColumnsRecursively(col.children, processor)
    }
  })
}

// 提取单元格插槽映射 - 基于作用域插槽的新架构
const cellSlots = computed(() => {
  const columns = effectiveGridOptions.value.columns || []

  // 如果没有列，直接返回空对象
  if (!Array.isArray(columns) || columns.length === 0) {
    return {}
  }

  // 使用更简单的缓存键生成
  const cacheKey = columns
    .map((col, index) => `${col.field || col.type || index}`)
    .join('|')

  // 使用缓存避免重复计算
  const cached = cellSlotsCache.get(cacheKey)
  if (cached) {
    return cached
  }

  const slots: Record<string, { field: string; column: any }> = {}

  processColumnsRecursively(columns, (col) => {
    if (col.field) {
      // 为每个有 field 的列创建一个作用域插槽
      const slotName = `cell-${col.field}`
      slots[slotName] = {
        field: col.field,
        column: col,
      }
    }
  })

  // 缓存结果，最多保存10个
  if (cellSlotsCache.size >= 10) {
    const firstKey = cellSlotsCache.keys().next().value
    cellSlotsCache.delete(firstKey)
  }
  cellSlotsCache.set(cacheKey, slots)

  return slots
})

// 处理列配置，为有 field 的列自动添加作用域插槽
const processedGridOptions = computed(() => {
  const options = { ...effectiveGridOptions.value }
  if (!options.columns) return options

  // 递归处理列配置
  const processColumns = (cols: any[]): any[] => {
    return cols.map((col) => {
      const processedCol = { ...col }

      // 为有 field 的列自动添加作用域插槽
      if (col.field) {
        processedCol.slots = {
          default: `cell-${col.field}`,
          ...processedCol.slots, // 保留现有的插槽配置
        }
      }

      // 处理子列
      if (col.children && Array.isArray(col.children)) {
        processedCol.children = processColumns(col.children)
      }

      return processedCol
    })
  }

  options.columns = processColumns(options.columns)
  return options
})

// 当grid实例准备好时，设置到dataGridInstance中
onMounted(() => {
  if (props.dataGridInstance && gridRef.value) {
    props.dataGridInstance._setGridRef(gridRef.value)
  }

  // 监听视窗尺寸变化
  window.addEventListener('resize', updateViewportHeight)
  window.addEventListener('scroll', updateViewportHeight)
})

// 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', updateViewportHeight)
  window.removeEventListener('scroll', updateViewportHeight)
})

// 监听gridRef变化，及时更新到dataGridInstance
watch(gridRef, (newGridRef) => {
  if (props.dataGridInstance && newGridRef) {
    props.dataGridInstance._setGridRef(newGridRef)
  }
})

// 暴露 vxe-grid 实例的方法给父组件
defineExpose({
  // 直接暴露 vxe-grid 实例
  getGridInstance: () => gridRef.value,
  // 暴露 ModelApi 实例
  getModelApi: () => props.dataGridInstance?.modelApi.value,
  // 暴露常用的 vxe-grid 方法
  getCheckboxRecords: () => gridRef.value?.getCheckboxRecords(),
  getRadioRecord: () => gridRef.value?.getRadioRecord(),
  getCurrentRecord: () => gridRef.value?.getCurrentRecord(),
  setCheckboxRow: (rows: any, checked: boolean) =>
    gridRef.value?.setCheckboxRow(rows, checked),
  setAllCheckboxRow: (checked: boolean) =>
    gridRef.value?.setAllCheckboxRow(checked),
  clearCheckboxRow: () => gridRef.value?.clearCheckboxRow(),
  clearCurrentRow: () => gridRef.value?.clearCurrentRow(),
  // 修复刷新方法，使用正确的 vxe-grid 方法
  refreshData: () => {
    if (props.dataGridInstance && props.dataGridInstance.refreshData) {
      // 调用 useDataGrid 中的 refreshData 方法
      props.dataGridInstance.refreshData()
    } else if (gridRef.value) {
      // 备用方案：使用 vxe-grid 的方法
      if (typeof gridRef.value.commitData === 'function') {
        gridRef.value.commitData()
      }
      // 或者重新设置数据来触发刷新
      const currentData = gridRef.value.getData()
      if (currentData) {
        gridRef.value.setData(currentData)
      }
    }
  },
  // 添加新的统一选择方法
  getSelection: () => props.dataGridInstance?.getSelection() || [],
  clearSelection: () => props.dataGridInstance?.clearSelection(),
  setSelection: (
    rows: any | any[],
    checked?: boolean,
    selectionType?: 'checkbox' | 'radio' | 'seq'
  ) => props.dataGridInstance?.setSelection(rows, checked, selectionType),
  setAllSelection: (checked?: boolean) =>
    props.dataGridInstance?.setAllSelection(checked),
  getSelectionCount: () => props.dataGridInstance?.getSelectionCount() || 0,
  hasSelection: () => props.dataGridInstance?.hasSelection() || false,
  // 可以根据需要添加更多方法
})
</script>

<style scoped>
/* DataGrid Footer 统计行样式 */
:deep(.vxe-table .vxe-table--footer-wrapper) {
  .vxe-footer--row {
    background-color: #fafafa;
    border-top: 2px solid #e8e8e8;
    font-weight: 500;

    .vxe-footer--column {
      color: #333;

      &:first-child {
        font-weight: bold;
      }
    }
  }
}

/* 支持自定义背景色 */
:deep(.vxe-table .vxe-table--footer-wrapper .vxe-footer--row[data-custom-bg]) {
  background-color: var(--footer-bg-color, #fafafa);
}

/* 支持固定 footer */
:deep(.vxe-table.is--footer-sticky .vxe-table--footer-wrapper) {
  position: sticky;
  bottom: 0;
  z-index: 10;
}

/* 响应式样式 */
@media (max-width: 768px) {
  :deep(.vxe-table .vxe-table--footer-wrapper) {
    .vxe-footer--row {
      font-size: 12px;
    }
  }
}
</style>
